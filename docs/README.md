# 项目文档结构说明

本目录包含项目的所有文档，按照以下三类结构组织：

## 📁 文档分类

### 1. 开发日志类 (`log{YYMMDD}/`)

存放项目开发过程中的详细记录，按日期组织。

**命名规范**: `log{YYMMDD}/`

- `log250104/` - 2025年1月4日的开发日志
- `log250627/` - 2025年6月27日的开发日志

**内容包括**:

- 每日开发进度记录
- 问题解决过程
- 技术决策记录
- 代码变更说明

### 2. 分析报告类 (`report/`)

存放技术分析、项目报告等正式文档。

**内容包括**:

- 技术架构分析
- 性能优化报告
- 代码质量分析
- 重构总结报告
- 部署指南
- 项目实施路线图

### 3. 文档模板示例类 (`templates/`)

存放标准化的文档格式模板。

**内容包括**:

- 开发日志模板 (`log-template.md`)
- 技术报告模板 (`report-template.md`)
- 架构文档模板 (`architecture-template.md`)

## 📋 使用指南

### 创建开发日志

1. 在根目录下创建新的日期目录：`docs/log{YYMMDD}/`
2. 使用模板 `templates/log-template.md` 创建日志文件
3. 按照模板格式填写内容

### 创建技术报告

1. 在 `docs/report/` 目录下创建新文件
2. 使用模板 `templates/report-template.md` 作为基础
3. 根据报告类型调整内容结构

### 更新架构文档

1. 使用模板 `templates/architecture-template.md`
2. 定期更新架构变更
3. 维护版本历史记录

## 🔍 文档索引

### 项目管理规范

- **[Augment User Guidelines](./USER_GUIDELINES.md)** - AI助手编码行为规范，确保代码质量和开发效率
- **[文档文件管理规范](./FILE_MANAGEMENT_GUIDELINES.md)** - docs目录统一管理规范，确保文档结构清晰

### 📊 技术报告

- **[技术报告目录](./report/README.md)** - 查看所有技术分析和优化报告
- [网格系统架构分析](./report/grid-system-architecture-analysis.md) - 核心架构设计分析
- [渲染架构重构报告](./report/rendering-architecture-refactor.md) - 性能优化重构记录

### 🔄 迁移文档

- **[迁移文档目录](./migration/README.md)** - 查看所有技术迁移记录
- [TanStack Query迁移](./migration/tanstack-query-migration.md) - 状态管理库迁移过程

### 📐 架构图表

- **[图表文档目录](./diagrams/README.md)** - 查看所有系统架构图和流程图
- [系统架构图](./diagrams/system-architecture.svg) - 整体系统架构
- [业务流程图](./diagrams/business-flow.svg) - 核心业务流程

### 📝 文档模板

- **[模板使用指南](./templates/README.md)** - 了解如何使用文档模板
- [技术报告模板](./templates/report-template.md) - 创建技术报告
- [架构文档模板](./templates/architecture-template.md) - 设计架构文档
- [开发日志模板](./templates/log-template.md) - 记录开发过程

### 项目核心信息

- **项目类型**: Monorepo全栈应用
- **前端技术栈**: Next.js 15.1.0 + React 18.3.1 + TypeScript 5.8.3
- **后端技术栈**: FastAPI 0.116.1 + Python 3.11+ + Pydantic 2.11.7
- **状态管理**: Zustand 5.0.6 + SWR 2.3.4
- **数据库**: Prisma 6.11.1 + SQLite/PostgreSQL
- **构建工具**: Turbo 2.3.0 + pnpm 9.15.0

## 📝 文档规范

### 命名规范

- **日志文件**: `log_{YYMMDD}_{HHMM}.md`
- **报告文件**: `{功能描述}_{YYMMDD}.md` (如: `前端数据流架构分析报告_250716.md`)
- **模板文件**: `{类型}-template.md`

### 格式规范

- 使用Markdown格式
- 统一的标题层级结构
- 包含必要的元数据（日期、作者、版本等）
- 使用emoji增强可读性

### 内容规范

- 清晰的问题描述和解决方案
- 详细的技术实现细节
- 完整的代码示例和配置
- 明确的后续计划和建议

## 🔄 维护说明

### 定期维护

- 每月整理和归档旧文档
- 更新文档索引和链接
- 检查文档的时效性

### 质量控制

- 确保文档格式统一
- 验证代码示例的正确性
- 保持文档内容的准确性

---

**文档结构版本**: v2.3
**最后更新**: 2025年7月30日
**维护人**: Augment Agent
**项目状态**: ✅ Monorepo架构完成，全栈开发就绪

## 📂 项目架构说明

### Monorepo结构概览

本项目采用现代化Monorepo架构，使用Turbo + pnpm进行工作区管理：

```text
cube1_group/
├── 📁 apps/                    # 应用程序目录
│   ├── 📁 frontend/           # Next.js 15.1.0 前端应用
│   │   ├── app/               # Next.js App Router
│   │   ├── components/        # UI组件库 (Radix UI + Tailwind)
│   │   ├── features/          # 业务功能模块
│   │   ├── lib/               # 工具库和API客户端
│   │   ├── stores/            # Zustand状态管理
│   │   ├── prisma/            # 数据库ORM和Schema
│   │   └── scripts/           # 自动化脚本
│   └── 📁 backend/            # FastAPI 0.116.1 后端应用
│       ├── app/               # FastAPI应用核心
│       ├── alembic/           # 数据库迁移
│       └── tests/             # 后端测试
├── 📁 docs/                   # 项目文档和开发日志
├── ⚙️ 配置文件 (package.json, turbo.json, pnpm-workspace.yaml)
└── 🎯 核心特性: 33×33网格 + 8色彩系统 + 版本控制
```

### 技术栈总览

| 层级          | 技术       | 版本    | 用途             |
| ------------- | ---------- | ------- | ---------------- |
| **前端框架**  | Next.js    | 15.1.0  | App Router, SSR  |
| **UI库**      | React      | 18.3.1  | 组件化开发       |
| **语言**      | TypeScript | 5.8.3   | 类型安全         |
| **状态管理**  | Zustand    | 5.0.6   | 轻量级状态管理   |
| **数据库ORM** | Prisma     | 6.11.1  | 类型安全的ORM    |
| **后端框架**  | FastAPI    | 0.104.1 | 高性能Python API |
| **构建工具**  | Turbo      | 2.3.0   | Monorepo构建系统 |
| **包管理**    | pnpm       | 9.15.0  | 高效包管理       |

### 开发工具链

- **代码质量**: ESLint + Prettier + TypeScript严格模式
- **测试框架**: Playwright 1.54.0 + Vitest 3.2.4
- **UI组件**: Radix UI + Tailwind CSS 3.4.17
- **部署方案**: Vercel + Docker
